import React, { useState, useEffect } from 'react';
import { Search, TrendingUp, Globe, Brain, Target, Clock, BarChart3 } from 'lucide-react';
import { showToast } from '../components/Toaster';

interface DiscoveredTicker {
  ticker: string;
  companyName: string;
  sector: string;
  marketCap: number;
  discoveryReason: string;
  impactScore: number;
  confidence: number;
  tradingOpportunities: number;
  riskLevel: 'low' | 'medium' | 'high';
  timeframe: string;
  discoveredAt: string;
}

interface DiscoveryStats {
  totalNewsAnalyzed: number;
  tickersDiscovered: number;
  averageConfidence: number;
  topSectors: string[];
  riskDistribution: { [key: string]: number };
}

export const Discovery: React.FC = () => {
  const [discoveredTickers, setDiscoveredTickers] = useState<DiscoveredTicker[]>([]);
  const [stats, setStats] = useState<DiscoveryStats | null>(null);
  const [isDiscovering, setIsDiscovering] = useState(false);

  useEffect(() => {
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/discovery/stats');
      const data = await response.json();
      if (data.success) {
        setStats(data.data);
      }
    } catch (error) {
      console.error('Error fetching discovery stats:', error);
    }
  };

  const discoverNewTickers = async () => {
    setIsDiscovering(true);
    try {
      showToast('Starting ticker discovery from global news...', 'info');

      const response = await fetch('http://localhost:3001/api/discovery/tickers');
      const data = await response.json();

      if (data.success) {
        setDiscoveredTickers(data.data.tickers);
        showToast(`Discovered ${data.data.count} new trading opportunities!`, 'success');
        fetchStats(); // Refresh stats
      } else {
        showToast('Failed to discover new tickers', 'error');
      }
    } catch (error) {
      console.error('Error discovering tickers:', error);
      showToast('Error during ticker discovery', 'error');
    } finally {
      setIsDiscovering(false);
    }
  };

  const getRiskBadgeColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'badge-success';
      case 'medium': return 'badge-warning';
      case 'high': return 'badge-danger';
      default: return 'badge-secondary';
    }
  };

  const formatMarketCap = (marketCap: number) => {
    if (marketCap >= 1e12) return `$${(marketCap / 1e12).toFixed(1)}T`;
    if (marketCap >= 1e9) return `$${(marketCap / 1e9).toFixed(1)}B`;
    if (marketCap >= 1e6) return `$${(marketCap / 1e6).toFixed(1)}M`;
    return `$${marketCap.toLocaleString()}`;
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Brain className="h-8 w-8 mr-3 text-primary-600" />
            AI Ticker Discovery
          </h1>
          <p className="text-gray-600 mt-2">
            Discover new trading opportunities from global news analysis using advanced AI
          </p>
        </div>
        <button
          onClick={discoverNewTickers}
          disabled={isDiscovering}
          className="btn btn-primary flex items-center"
        >
          {isDiscovering ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Analyzing...
            </>
          ) : (
            <>
              <Search className="h-4 w-4 mr-2" />
              Discover New Tickers
            </>
          )}
        </button>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="card">
            <div className="flex items-center">
              <Globe className="h-8 w-8 text-primary-600 mr-3" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{stats.totalNewsAnalyzed}</div>
                <div className="text-sm text-gray-600">News Articles Analyzed</div>
              </div>
            </div>
          </div>
          
          <div className="card">
            <div className="flex items-center">
              <Target className="h-8 w-8 text-success-600 mr-3" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{stats.tickersDiscovered}</div>
                <div className="text-sm text-gray-600">Tickers Discovered</div>
              </div>
            </div>
          </div>
          
          <div className="card">
            <div className="flex items-center">
              <BarChart3 className="h-8 w-8 text-warning-600 mr-3" />
              <div>
                <div className="text-2xl font-bold text-gray-900">{stats.averageConfidence}%</div>
                <div className="text-sm text-gray-600">Average Confidence</div>
              </div>
            </div>
          </div>
          
          <div className="card">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-primary-600 mr-3" />
              <div>
                <div className="text-sm font-medium text-gray-700">Top Sectors</div>
                <div className="text-xs text-gray-600">
                  {stats.topSectors.slice(0, 2).join(', ')}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* How It Works */}
      <div className="card mb-8">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">How AI Ticker Discovery Works</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <div className="bg-primary-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
              <Globe className="h-6 w-6 text-primary-600" />
            </div>
            <h3 className="font-medium text-gray-900 mb-2">Global News Monitoring</h3>
            <p className="text-sm text-gray-600">
              Scan global news from Reuters, Bloomberg, BBC, and other trusted sources
            </p>
          </div>
          
          <div className="text-center">
            <div className="bg-success-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
              <Brain className="h-6 w-6 text-success-600" />
            </div>
            <h3 className="font-medium text-gray-900 mb-2">AI Impact Analysis</h3>
            <p className="text-sm text-gray-600">
              Use GPT-4 to analyze potential market impacts and identify affected sectors
            </p>
          </div>
          
          <div className="text-center">
            <div className="bg-warning-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
              <Target className="h-6 w-6 text-warning-600" />
            </div>
            <h3 className="font-medium text-gray-900 mb-2">Ticker Extraction</h3>
            <p className="text-sm text-gray-600">
              Automatically identify relevant stock tickers and trading opportunities
            </p>
          </div>
          
          <div className="text-center">
            <div className="bg-danger-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
              <BarChart3 className="h-6 w-6 text-danger-600" />
            </div>
            <h3 className="font-medium text-gray-900 mb-2">Opportunity Scoring</h3>
            <p className="text-sm text-gray-600">
              Score and rank opportunities based on impact, confidence, and risk
            </p>
          </div>
        </div>
      </div>

      {/* Discovered Tickers */}
      {discoveredTickers.length > 0 && (
        <div className="card">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">
              Recently Discovered Opportunities
            </h2>
            <span className="text-sm text-gray-600">
              {discoveredTickers.length} opportunities found
            </span>
          </div>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ticker
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Company
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Impact Score
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Risk Level
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Opportunities
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Discovery Reason
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {discoveredTickers.map((ticker, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="font-medium text-gray-900">{ticker.ticker}</div>
                        <div className="ml-2 text-xs text-gray-500">{ticker.sector}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">{ticker.companyName}</div>
                      <div className="text-xs text-gray-500">
                        {formatMarketCap(ticker.marketCap)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className={`text-lg font-bold ${
                          ticker.impactScore > 0 ? 'text-success-600' : 'text-danger-600'
                        }`}>
                          {ticker.impactScore > 0 ? '+' : ''}{ticker.impactScore.toFixed(0)}
                        </div>
                        <div className="ml-2 text-xs text-gray-500">
                          {ticker.confidence}% conf.
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`badge ${getRiskBadgeColor(ticker.riskLevel)}`}>
                        {ticker.riskLevel}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-center">
                      <div className="text-lg font-bold text-primary-600">
                        {ticker.tradingOpportunities}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 max-w-xs truncate">
                        {ticker.discoveryReason}
                      </div>
                      <div className="flex items-center mt-1 text-xs text-gray-500">
                        <Clock className="h-3 w-3 mr-1" />
                        {ticker.timeframe}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Empty State */}
      {discoveredTickers.length === 0 && !isDiscovering && (
        <div className="card text-center py-12">
          <Search className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Tickers Discovered Yet
          </h3>
          <p className="text-gray-600 mb-6">
            Click "Discover New Tickers" to start analyzing global news for trading opportunities
          </p>
          <button
            onClick={discoverNewTickers}
            className="btn btn-primary"
          >
            <Brain className="h-4 w-4 mr-2" />
            Start Discovery
          </button>
        </div>
      )}

      {/* Risk Distribution */}
      {stats && (
        <div className="card mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Risk Distribution</h3>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-success-600">{stats.riskDistribution.low || 0}</div>
              <div className="text-sm text-gray-600">Low Risk</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-warning-600">{stats.riskDistribution.medium || 0}</div>
              <div className="text-sm text-gray-600">Medium Risk</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-danger-600">{stats.riskDistribution.high || 0}</div>
              <div className="text-sm text-gray-600">High Risk</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
