import React, { useEffect, useRef, useState } from 'react';
import { create<PERSON>hart, IChartApi, ISeriesApi, CandlestickData, LineData, HistogramData } from 'lightweight-charts';
import { OHLCV, AdvancedTechnicalIndicators } from '../../types';
import { ChartErrorBoundary } from '../ErrorBoundary';
import { ChartLoader } from '../LoadingSpinner';

interface CandlestickChartProps {
  data: OHLCV[];
  indicators?: AdvancedTechnicalIndicators;
  height?: number;
  showVolume?: boolean;
  showIndicators?: boolean;
  ticker: string;
}

export const CandlestickChart: React.FC<CandlestickChartProps> = ({
  data,
  indicators,
  height = 600,
  showVolume = true,
  showIndicators = true,
  ticker
}) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null);
  const volumeSeriesRef = useRef<ISeriesApi<'Histogram'> | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Helper function to extract timestamp from OHLCV data
  const getTimestamp = (candle: any) => {
    const timestamp = candle.date || candle.timestamp;
    const time = timestamp instanceof Date ? timestamp.getTime() : new Date(timestamp).getTime();
    return Math.floor(time / 1000) as any;
  };

  useEffect(() => {
    // Add a small delay to ensure the container ref is available
    const initChart = () => {
      if (!chartContainerRef.current || !data.length) {
        return;
      }

      // Cleanup existing chart
      if (chartRef.current) {
        chartRef.current.remove();
        chartRef.current = null;
      }

      // Create chart
      const chart = createChart(chartContainerRef.current, {
        width: chartContainerRef.current.clientWidth,
        height: height,
        layout: {
          background: { color: '#ffffff' },
          textColor: '#333333',
        },
        grid: {
          vertLines: { color: '#f0f0f0' },
          horzLines: { color: '#f0f0f0' },
        },
        crosshair: {
          mode: 1,
        },
        rightPriceScale: {
          borderColor: '#cccccc',
        },
        timeScale: {
          borderColor: '#cccccc',
          timeVisible: true,
          secondsVisible: false,
        },
      });

      chartRef.current = chart;

      // Add candlestick series
      const candlestickSeries = chart.addCandlestickSeries({
        upColor: '#22c55e', // success-500
        downColor: '#ef4444', // danger-500
        borderDownColor: '#ef4444',
        borderUpColor: '#22c55e',
        wickDownColor: '#ef4444',
        wickUpColor: '#22c55e',
      });

      candlestickSeriesRef.current = candlestickSeries;

      // Convert OHLCV data to lightweight-charts format
      const candlestickData: CandlestickData[] = data.map(candle => ({
        time: getTimestamp(candle),
        open: candle.open,
        high: candle.high,
        low: candle.low,
        close: candle.close,
      }));

      candlestickSeries.setData(candlestickData);

      // Add volume series if enabled
      if (showVolume) {
        const volumeSeries = chart.addHistogramSeries({
          color: '#3b82f6', // primary-500
          priceFormat: {
            type: 'volume',
          },
          priceScaleId: 'volume',
        });

        volumeSeriesRef.current = volumeSeries;

        const volumeData: HistogramData[] = data.map(candle => ({
          time: getTimestamp(candle),
          value: candle.volume,
          color: candle.close >= candle.open ? '#22c55e' : '#ef4444',
        }));

        volumeSeries.setData(volumeData);

        // Scale volume to 25% of chart height
        chart.priceScale('volume').applyOptions({
          scaleMargins: {
            top: 0.75,
            bottom: 0,
          },
        });
      }

      // Add technical indicators if available and enabled
      if (showIndicators && indicators) {
        addTechnicalIndicators(chart, data, indicators);
      }

      // Handle resize
      const handleResize = () => {
        if (chartContainerRef.current && chart) {
          chart.applyOptions({
            width: chartContainerRef.current.clientWidth,
          });
        }
      };

      window.addEventListener('resize', handleResize);
      setIsLoading(false);

      return () => {
        window.removeEventListener('resize', handleResize);
        if (chart) {
          chart.remove();
        }
      };
    };

    // Use setTimeout to ensure the DOM is ready
    const timeoutId = setTimeout(initChart, 0);

    return () => {
      clearTimeout(timeoutId);
    };
  }, [data, indicators, height, showVolume, showIndicators]);

  const addTechnicalIndicators = (
    chart: IChartApi,
    ohlcvData: OHLCV[],
    indicators: AdvancedTechnicalIndicators
  ) => {
    // Add SMA lines
    if (indicators.sma) {
      const smaShortSeries = chart.addLineSeries({
        color: '#f59e0b', // warning-500
        lineWidth: 2,
        title: 'SMA 20',
      });

      const smaLongSeries = chart.addLineSeries({
        color: '#8b5cf6', // purple-500
        lineWidth: 2,
        title: 'SMA 50',
      });

      // Create SMA data points (simplified - in production, calculate actual SMA values)
      const smaData: LineData[] = ohlcvData.map(candle => ({
        time: getTimestamp(candle),
        value: indicators.sma.short,
      }));

      const smaLongData: LineData[] = ohlcvData.map(candle => ({
        time: getTimestamp(candle),
        value: indicators.sma.long,
      }));

      smaShortSeries.setData(smaData);
      smaLongSeries.setData(smaLongData);
    }

    // Add Bollinger Bands
    if (indicators.bollingerBands) {
      const upperBandSeries = chart.addLineSeries({
        color: '#6b7280', // gray-500
        lineWidth: 1,
        lineStyle: 2, // dashed
        title: 'BB Upper',
      });

      const lowerBandSeries = chart.addLineSeries({
        color: '#6b7280', // gray-500
        lineWidth: 1,
        lineStyle: 2, // dashed
        title: 'BB Lower',
      });

      const middleBandSeries = chart.addLineSeries({
        color: '#9ca3af', // gray-400
        lineWidth: 1,
        title: 'BB Middle',
      });

      const upperData: LineData[] = ohlcvData.map(candle => ({
        time: getTimestamp(candle),
        value: indicators.bollingerBands.upper,
      }));

      const lowerData: LineData[] = ohlcvData.map(candle => ({
        time: getTimestamp(candle),
        value: indicators.bollingerBands.lower,
      }));

      const middleData: LineData[] = ohlcvData.map(candle => ({
        time: getTimestamp(candle),
        value: indicators.bollingerBands.middle,
      }));

      upperBandSeries.setData(upperData);
      lowerBandSeries.setData(lowerData);
      middleBandSeries.setData(middleData);
    }

    // Add support and resistance levels
    if (indicators.supportResistance) {
      indicators.supportResistance.support.forEach((level, index) => {
        const supportSeries = chart.addLineSeries({
          color: '#22c55e', // success-500
          lineWidth: 1,
          lineStyle: 3, // dotted
          title: `Support ${index + 1}`,
        });

        const supportData: LineData[] = ohlcvData.map(candle => ({
          time: getTimestamp(candle),
          value: level,
        }));

        supportSeries.setData(supportData);
      });

      indicators.supportResistance.resistance.forEach((level, index) => {
        const resistanceSeries = chart.addLineSeries({
          color: '#ef4444', // danger-500
          lineWidth: 1,
          lineStyle: 3, // dotted
          title: `Resistance ${index + 1}`,
        });

        const resistanceData: LineData[] = ohlcvData.map(candle => ({
          time: getTimestamp(candle),
          value: level,
        }));

        resistanceSeries.setData(resistanceData);
      });
    }

    // Add Ichimoku Cloud if available
    if (indicators.ichimoku) {
      const ichimoku = indicators.ichimoku;
      const tenkanSeries = chart.addLineSeries({
        color: '#06b6d4', // cyan-500
        lineWidth: 1,
        title: 'Tenkan-sen',
      });

      const kijunSeries = chart.addLineSeries({
        color: '#8b5cf6', // purple-500
        lineWidth: 1,
        title: 'Kijun-sen',
      });

      const senkouSpanASeries = chart.addLineSeries({
        color: '#22c55e', // success-500
        lineWidth: 1,
        lineStyle: 2, // dashed
        title: 'Senkou Span A',
      });

      const senkouSpanBSeries = chart.addLineSeries({
        color: '#ef4444', // danger-500
        lineWidth: 1,
        lineStyle: 2, // dashed
        title: 'Senkou Span B',
      });

      const chikouSeries = chart.addLineSeries({
        color: '#f59e0b', // warning-500
        lineWidth: 1,
        lineStyle: 3, // dotted
        title: 'Chikou Span',
      });

      const ichimokuData = ohlcvData.map(candle => ({
        time: getTimestamp(candle),
      }));

      tenkanSeries.setData(ichimokuData.map(d => ({ ...d, value: ichimoku.tenkanSen })));
      kijunSeries.setData(ichimokuData.map(d => ({ ...d, value: ichimoku.kijunSen })));
      senkouSpanASeries.setData(ichimokuData.map(d => ({ ...d, value: ichimoku.senkouSpanA })));
      senkouSpanBSeries.setData(ichimokuData.map(d => ({ ...d, value: ichimoku.senkouSpanB })));
      chikouSeries.setData(ichimokuData.map(d => ({ ...d, value: ichimoku.chikouSpan })));
    }

    // Add EMA lines (calculated from SMA for simplicity)
    if (indicators.sma) {
      const emaShortSeries = chart.addLineSeries({
        color: '#ec4899', // pink-500
        lineWidth: 1,
        lineStyle: 2, // dashed
        title: 'EMA 12',
      });

      const emaLongSeries = chart.addLineSeries({
        color: '#14b8a6', // teal-500
        lineWidth: 1,
        lineStyle: 2, // dashed
        title: 'EMA 26',
      });

      // Simplified EMA calculation (in production, use proper EMA formula)
      const emaShortData: LineData[] = ohlcvData.map(candle => ({
        time: getTimestamp(candle),
        value: indicators.sma.short * 0.98, // Simplified EMA approximation
      }));

      const emaLongData: LineData[] = ohlcvData.map(candle => ({
        time: getTimestamp(candle),
        value: indicators.sma.long * 0.99, // Simplified EMA approximation
      }));

      emaShortSeries.setData(emaShortData);
      emaLongSeries.setData(emaLongData);
    }

    // Add Fibonacci retracement levels if available
    if (indicators.fibonacci && indicators.fibonacci.retracements.length > 0) {
      indicators.fibonacci.retracements.forEach((fib) => {
        const fibSeries = chart.addLineSeries({
          color: '#a855f7', // purple-500 with varying opacity
          lineWidth: 1,
          lineStyle: 3, // dotted
          title: `Fib ${(fib.level * 100).toFixed(1)}%`,
        });

        const fibData: LineData[] = ohlcvData.map(candle => ({
          time: getTimestamp(candle),
          value: fib.price,
        }));

        fibSeries.setData(fibData);
      });
    }

    // Add pattern recognition overlays
    if (indicators.patternRecognition) {
      addPatternRecognitionOverlays(chart, ohlcvData, indicators.patternRecognition);
    }
  };

  const addPatternRecognitionOverlays = (
    chart: IChartApi,
    ohlcvData: OHLCV[],
    patternRecognition: any
  ) => {
    // Add pattern markers for detected patterns
    if (patternRecognition.detectedPatterns && patternRecognition.detectedPatterns.length > 0) {
      patternRecognition.detectedPatterns.forEach((pattern: string, index: number) => {
        // Create markers for different pattern types
        const markerPosition = Math.floor(ohlcvData.length * (0.7 + index * 0.1)); // Spread patterns across chart
        const markerTime = getTimestamp(ohlcvData[markerPosition]);
        const markerPrice = ohlcvData[markerPosition].high * (1.02 + index * 0.01); // Stagger heights

        // Add pattern annotation as a line series with a single point
        const patternSeries = chart.addLineSeries({
          color: pattern.toLowerCase().includes('bullish') || pattern.toLowerCase().includes('ascending') ? '#22c55e' : '#ef4444',
          lineWidth: 3,
          pointMarkersVisible: true,
          title: pattern,
        });

        patternSeries.setData([{
          time: markerTime,
          value: markerPrice,
        }]);

        // Add pattern breakout/breakdown signals
        if (pattern.toLowerCase().includes('flag') || pattern.toLowerCase().includes('triangle')) {
          const breakoutSeries = chart.addLineSeries({
            color: '#f59e0b', // warning color for breakout signals
            lineWidth: 2,
            lineStyle: 2, // dashed
            title: `${pattern} Breakout Zone`,
          });

          // Create breakout zone line
          const breakoutData = ohlcvData.slice(-20).map(candle => ({
            time: getTimestamp(candle),
            value: candle.high * 1.01, // Slightly above recent highs
          }));

          breakoutSeries.setData(breakoutData);
        }
      });
    }

    // Add trend strength indicators
    if (patternRecognition.trendStrength) {
      const trendColor = patternRecognition.trendDirection === 'uptrend' ? '#22c55e' :
                        patternRecognition.trendDirection === 'downtrend' ? '#ef4444' : '#6b7280';

      const trendSeries = chart.addLineSeries({
        color: trendColor,
        lineWidth: 2,
        lineStyle: 3, // dotted
        title: `Trend (${patternRecognition.trendStrength})`,
      });

      // Create trend line based on first and last data points
      const firstPoint = ohlcvData[0];
      const lastPoint = ohlcvData[ohlcvData.length - 1];

      const trendData = [
        {
          time: getTimestamp(firstPoint),
          value: patternRecognition.trendDirection === 'uptrend' ? firstPoint.low : firstPoint.high,
        },
        {
          time: getTimestamp(lastPoint),
          value: patternRecognition.trendDirection === 'uptrend' ? lastPoint.high : lastPoint.low,
        }
      ];

      trendSeries.setData(trendData);
    }
  };

  if (isLoading) {
    return <ChartLoader height={height} text={`Loading ${ticker} chart...`} />;
  }

  return (
    <ChartErrorBoundary>
      <div className="w-full">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">{ticker} Price Chart</h3>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-success-500 rounded"></div>
              <span className="text-sm text-gray-600">Bullish</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-danger-500 rounded"></div>
              <span className="text-sm text-gray-600">Bearish</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-warning-500 rounded"></div>
              <span className="text-sm text-gray-600">SMA 20</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-purple-500 rounded"></div>
              <span className="text-sm text-gray-600">SMA 50</span>
            </div>
            {indicators?.bollingerBands && (
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-gray-500 rounded"></div>
                <span className="text-sm text-gray-600">Bollinger Bands</span>
              </div>
            )}
            {indicators?.ichimoku && (
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-cyan-500 rounded"></div>
                <span className="text-sm text-gray-600">Ichimoku</span>
              </div>
            )}
            {indicators?.supportResistance && (
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded"></div>
                <span className="text-sm text-gray-600">S/R Levels</span>
              </div>
            )}
          </div>
        </div>

        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <div ref={chartContainerRef} className="w-full" />
        </div>

      {indicators && (
        <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm font-medium text-gray-700">RSI</div>
            <div className={`text-lg font-bold ${
              indicators.rsi > 70 ? 'text-danger-600' : 
              indicators.rsi < 30 ? 'text-success-600' : 'text-gray-900'
            }`}>
              {indicators.rsi.toFixed(1)}
            </div>
          </div>
          
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm font-medium text-gray-700">MACD</div>
            <div className={`text-lg font-bold ${
              indicators.macd.histogram > 0 ? 'text-success-600' : 'text-danger-600'
            }`}>
              {indicators.macd.macd.toFixed(3)}
            </div>
          </div>
          
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm font-medium text-gray-700">Volume Trend</div>
            <div className={`text-lg font-bold ${
              indicators.volumeAnalysis.volumeTrend === 'increasing' ? 'text-success-600' : 
              indicators.volumeAnalysis.volumeTrend === 'decreasing' ? 'text-danger-600' : 'text-gray-900'
            }`}>
              {indicators.volumeAnalysis.volumeTrend}
            </div>
          </div>
          
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm font-medium text-gray-700">Trend</div>
            <div className={`text-lg font-bold ${
              indicators.patternRecognition.trendDirection === 'uptrend' ? 'text-success-600' : 
              indicators.patternRecognition.trendDirection === 'downtrend' ? 'text-danger-600' : 'text-gray-900'
            }`}>
              {indicators.patternRecognition.trendDirection}
            </div>
          </div>
        </div>
      )}
      </div>
    </ChartErrorBoundary>
  );
};
