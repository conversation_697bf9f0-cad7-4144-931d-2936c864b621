import { GoogleGenerativeAI } from '@google/generative-ai';
import { SentimentAnalysis, NewsArticle } from '../types';
import { SENTIMENT_PROMPT } from '../config/settings';
import { logger } from '../utils/logger';

export class SentimentAnalysisService {
  private genAI: GoogleGenerativeAI;
  private model: any;

  constructor(apiKey: string) {
    if (!apiKey) {
      throw new Error('Google AI API key is required');
    }
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.model = this.genAI.getGenerativeModel({ model: 'gemma-3-27b-it' });
  }

  /**
   * Analyze sentiment of a news article
   */
  public async analyzeSentiment(article: NewsArticle, ticker: string): Promise<SentimentAnalysis> {
    try {
      const prompt = SENTIMENT_PROMPT
        .replace('{ticker}', ticker)
        .replace('{title}', article.title)
        .replace('{content}', article.content.substring(0, 1000)); // Limit content length

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      // Parse JSON response
      const sentimentData = this.parseSentimentResponse(text);
      
      logger.info('Sentiment analysis completed', { 
        ticker, 
        sentiment: sentimentData.sentiment,
        confidence: sentimentData.confidence 
      });

      return sentimentData;
    } catch (error) {
      logger.error('Error in sentiment analysis:', error);
      
      // Fallback to neutral sentiment
      return {
        sentiment: 'Neutral',
        confidence: 'Low',
        summary: 'Unable to analyze sentiment due to technical error',
        shortTermImpact: false,
        reasoning: 'Analysis failed, defaulting to neutral'
      };
    }
  }

  /**
   * Parse the AI response to extract sentiment data
   */
  private parseSentimentResponse(text: string): SentimentAnalysis {
    try {
      // Try to extract JSON from the response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const jsonStr = jsonMatch[0];
        const data = JSON.parse(jsonStr);
        
        return {
          sentiment: data.sentiment || 'Neutral',
          confidence: data.confidence || 'Low',
          summary: data.summary || 'No summary available',
          shortTermImpact: data.shortTermImpact || false,
          reasoning: data.reasoning || 'No reasoning provided'
        };
      }

      // Fallback parsing if JSON extraction fails
      return this.fallbackSentimentParsing(text);
    } catch (error) {
      logger.error('Error parsing sentiment response:', error);
      return this.fallbackSentimentParsing(text);
    }
  }

  /**
   * Fallback sentiment parsing when JSON parsing fails
   */
  private fallbackSentimentParsing(text: string): SentimentAnalysis {
    const lowerText = text.toLowerCase();
    
    let sentiment: 'Positive' | 'Neutral' | 'Negative' = 'Neutral';
    let confidence: 'High' | 'Medium' | 'Low' = 'Low';
    let shortTermImpact = false;

    // Determine sentiment
    if (lowerText.includes('positive') || lowerText.includes('bullish') || lowerText.includes('buy')) {
      sentiment = 'Positive';
    } else if (lowerText.includes('negative') || lowerText.includes('bearish') || lowerText.includes('sell')) {
      sentiment = 'Negative';
    }

    // Determine confidence
    if (lowerText.includes('high confidence') || lowerText.includes('strong')) {
      confidence = 'High';
    } else if (lowerText.includes('medium confidence') || lowerText.includes('moderate')) {
      confidence = 'Medium';
    }

    // Determine short-term impact
    if (lowerText.includes('short term') || lowerText.includes('immediate') || lowerText.includes('24-48 hours')) {
      shortTermImpact = true;
    }

    return {
      sentiment,
      confidence,
      summary: text.substring(0, 200) + '...',
      shortTermImpact,
      reasoning: 'Parsed from AI response using fallback method'
    };
  }

  /**
   * Analyze multiple articles and aggregate sentiment
   */
  public async analyzeMultipleArticles(articles: NewsArticle[], ticker: string): Promise<{
    overallSentiment: 'Positive' | 'Neutral' | 'Negative';
    averageConfidence: 'High' | 'Medium' | 'Low';
    summary: string;
    articlesWithSentiment: (NewsArticle & { sentiment: SentimentAnalysis })[];
  }> {
    const articlesWithSentiment: (NewsArticle & { sentiment: SentimentAnalysis })[] = [];
    
    // Analyze each article
    for (const article of articles) {
      const sentiment = await this.analyzeSentiment(article, ticker);
      articlesWithSentiment.push({ ...article, sentiment });
    }

    // Aggregate sentiment
    const sentimentCounts = {
      Positive: 0,
      Neutral: 0,
      Negative: 0
    };

    const confidenceScores = {
      High: 3,
      Medium: 2,
      Low: 1
    };

    let totalConfidence = 0;
    let totalArticles = articlesWithSentiment.length;

    articlesWithSentiment.forEach(article => {
      sentimentCounts[article.sentiment.sentiment]++;
      totalConfidence += confidenceScores[article.sentiment.confidence];
    });

    // Determine overall sentiment
    let overallSentiment: 'Positive' | 'Neutral' | 'Negative' = 'Neutral';
    if (sentimentCounts.Positive > sentimentCounts.Negative && sentimentCounts.Positive > sentimentCounts.Neutral) {
      overallSentiment = 'Positive';
    } else if (sentimentCounts.Negative > sentimentCounts.Positive && sentimentCounts.Negative > sentimentCounts.Neutral) {
      overallSentiment = 'Negative';
    }

    // Calculate average confidence
    const averageConfidenceScore = totalConfidence / totalArticles;
    let averageConfidence: 'High' | 'Medium' | 'Low' = 'Low';
    if (averageConfidenceScore >= 2.5) {
      averageConfidence = 'High';
    } else if (averageConfidenceScore >= 1.5) {
      averageConfidence = 'Medium';
    }

    // Generate summary
    const summary = `Analyzed ${totalArticles} articles. Overall sentiment: ${overallSentiment} with ${averageConfidence} confidence. Positive: ${sentimentCounts.Positive}, Neutral: ${sentimentCounts.Neutral}, Negative: ${sentimentCounts.Negative}`;

    return {
      overallSentiment,
      averageConfidence,
      summary,
      articlesWithSentiment
    };
  }

  /**
   * Get sentiment score for numerical calculations
   */
  public getSentimentScore(sentiment: 'Positive' | 'Neutral' | 'Negative'): number {
    switch (sentiment) {
      case 'Positive': return 1;
      case 'Neutral': return 0;
      case 'Negative': return -1;
      default: return 0;
    }
  }
} 