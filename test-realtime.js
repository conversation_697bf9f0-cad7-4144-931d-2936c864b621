// Real-time data testing script
const baseUrl = 'http://localhost:3001/api';

async function fetchWithTimestamp(endpoint, description) {
  try {
    const startTime = Date.now();
    const response = await fetch(`${baseUrl}${endpoint}`);
    const endTime = Date.now();
    const data = await response.json();
    
    console.log(`\n📊 ${description}`);
    console.log(`⏱️  Response time: ${endTime - startTime}ms`);
    console.log(`🕐 Timestamp: ${new Date().toISOString()}`);
    
    if (response.ok && data.success) {
      console.log(`✅ Success - Data received`);
      
      // Show relevant data based on endpoint
      if (endpoint.includes('dashboard')) {
        const summary = data.data.summary;
        console.log(`📈 Summary: ${summary.totalTickers} tickers, ${summary.buySignals} buy, ${summary.sellSignals} sell, ${summary.holdSignals} hold`);
        console.log(`💭 Avg Sentiment: ${summary.averageSentiment}`);
      } else if (endpoint.includes('tickers') && !endpoint.includes('dashboard')) {
        console.log(`📋 Tickers count: ${data.data.length}`);
        if (data.data.length > 0) {
          const latest = data.data[0];
          console.log(`🎯 Latest: ${latest.ticker} - $${latest.currentPrice} (${latest.recommendation.action})`);
          console.log(`📅 Last updated: ${latest.lastUpdated}`);
        }
      } else if (endpoint.includes('logs')) {
        console.log(`📝 Logs count: ${data.data.length}`);
        if (data.data.length > 0) {
          const latest = data.data[0];
          console.log(`🔍 Latest log: ${latest.type} - ${latest.ticker} - ${latest.message.substring(0, 50)}...`);
        }
      }
    } else {
      console.log(`❌ Failed: ${data.error || 'Unknown error'}`);
    }
    
    return data;
  } catch (error) {
    console.log(`💥 Network Error: ${error.message}`);
    return null;
  }
}

async function testDataConsistency() {
  console.log('\n🔄 Testing Data Consistency');
  console.log('=' .repeat(40));
  
  // Fetch the same data multiple times to check consistency
  const results = [];
  for (let i = 0; i < 3; i++) {
    console.log(`\n📡 Fetch ${i + 1}/3`);
    const data = await fetchWithTimestamp('/tickers', `Get all tickers (attempt ${i + 1})`);
    if (data && data.success) {
      results.push({
        count: data.data.length,
        timestamp: data.timestamp,
        tickers: data.data.map(t => ({ ticker: t.ticker, price: t.currentPrice, updated: t.lastUpdated }))
      });
    }
    
    // Wait 2 seconds between requests
    if (i < 2) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  // Analyze consistency
  if (results.length > 1) {
    console.log('\n📊 Consistency Analysis:');
    const firstCount = results[0].count;
    const allSameCount = results.every(r => r.count === firstCount);
    console.log(`📈 Ticker count consistent: ${allSameCount ? '✅' : '❌'} (${firstCount})`);
    
    // Check if any prices changed (indicating real-time updates)
    if (results.length >= 2 && results[0].tickers.length > 0) {
      const ticker = results[0].tickers[0].ticker;
      const price1 = results[0].tickers[0].price;
      const price2 = results[1].tickers.find(t => t.ticker === ticker)?.price;
      
      if (price1 !== undefined && price2 !== undefined) {
        console.log(`💰 ${ticker} price: $${price1} → $${price2} ${price1 === price2 ? '(stable)' : '(updated!)'}`);
      }
    }
  }
}

async function testSystemLoad() {
  console.log('\n🚀 Testing System Load');
  console.log('=' .repeat(40));
  
  const endpoints = [
    '/tickers',
    '/tickers/dashboard/summary',
    '/logs?limit=10',
    '/discovery/stats'
  ];
  
  const startTime = Date.now();
  const promises = endpoints.map(endpoint => 
    fetchWithTimestamp(endpoint, `Concurrent request to ${endpoint}`)
  );
  
  try {
    await Promise.all(promises);
    const endTime = Date.now();
    console.log(`\n⚡ All concurrent requests completed in ${endTime - startTime}ms`);
  } catch (error) {
    console.log(`💥 Concurrent request failed: ${error.message}`);
  }
}

async function monitorRealTimeUpdates() {
  console.log('\n👁️  Monitoring Real-time Updates (30 seconds)');
  console.log('=' .repeat(40));
  
  let previousData = null;
  const monitorDuration = 30000; // 30 seconds
  const checkInterval = 5000; // 5 seconds
  const startTime = Date.now();
  
  while (Date.now() - startTime < monitorDuration) {
    const data = await fetchWithTimestamp('/tickers', 'Monitor check');
    
    if (data && data.success && previousData) {
      // Check for changes
      const changes = [];
      data.data.forEach(current => {
        const previous = previousData.data.find(p => p.ticker === current.ticker);
        if (previous) {
          if (current.currentPrice !== previous.currentPrice) {
            changes.push(`${current.ticker}: $${previous.currentPrice} → $${current.currentPrice}`);
          }
          if (current.lastUpdated !== previous.lastUpdated) {
            changes.push(`${current.ticker}: Updated at ${new Date(current.lastUpdated).toLocaleTimeString()}`);
          }
        }
      });
      
      if (changes.length > 0) {
        console.log(`🔄 Changes detected:`);
        changes.forEach(change => console.log(`   ${change}`));
      } else {
        console.log(`📊 No changes detected`);
      }
    }
    
    previousData = data;
    
    // Wait before next check
    await new Promise(resolve => setTimeout(resolve, checkInterval));
  }
  
  console.log(`\n✅ Monitoring completed`);
}

async function runRealTimeTests() {
  console.log('🔄 Starting Real-time Data & Stability Tests');
  console.log('=' .repeat(50));
  
  // Test 1: Basic connectivity and response times
  await fetchWithTimestamp('/tickers', 'Basic connectivity test');
  await fetchWithTimestamp('/tickers/dashboard/summary', 'Dashboard connectivity test');
  
  // Test 2: Data consistency
  await testDataConsistency();
  
  // Test 3: System load handling
  await testSystemLoad();
  
  // Test 4: Real-time monitoring
  await monitorRealTimeUpdates();
  
  console.log('\n🏁 Real-time Tests Complete');
  console.log('=' .repeat(50));
}

// Run the tests
runRealTimeTests().catch(console.error);
