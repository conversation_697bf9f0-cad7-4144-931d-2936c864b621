import { useQuery } from 'react-query';
import { Link } from 'react-router-dom';
import { TrendingUp, TrendingDown, Minus, DollarSign, Activity, RefreshCw } from 'lucide-react';
import { tickerApi } from '../services/api';
import { DashboardData, TickerData } from '../types';
import { showToast } from '../components/Toaster';
import { useEffect, useState } from 'react';
import { animations, conditionalAnimation } from '../utils/animations';
import { ErrorBoundary } from '../components/ErrorBoundary';
import { SkeletonCard } from '../components/LoadingSpinner';

export const Dashboard = () => {
  const { data: dashboardData, isLoading, error } = useQuery<DashboardData>(
    'dashboard',
    tickerApi.getDashboardSummary,
    {
      refetchInterval: 30000, // Refetch every 30 seconds
      onError: () => showToast('Failed to load dashboard data', 'error'),
    }
  );

  const [tickers, setTickers] = useState<TickerData[]>([]);

  useEffect(() => {
    const fetchTickers = async () => {
      try {
        const response = await fetch('/api/tickers');
        const data = await response.json();
        console.log('Fetched data:', data);
        if (data.success) {
          setTickers(data.data);
        }
      } catch (error) {
        console.error('Error fetching tickers:', error);
      }
    };

    fetchTickers();
    const interval = setInterval(fetchTickers, 60000); // Refresh every minute

    return () => clearInterval(interval);
  }, []);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="h-8 bg-gray-200 rounded w-64 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-96 mt-2 animate-pulse"></div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, i) => (
            <SkeletonCard key={i} />
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <SkeletonCard key={i} />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 text-lg font-medium">Failed to load dashboard</div>
        <div className="text-gray-600 mt-2">Please try again later</div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-600">No data available</div>
      </div>
    );
  }

  const { summary } = dashboardData;

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'Buy':
        return <TrendingUp className="h-5 w-5 text-success-600" />;
      case 'Sell':
        return <TrendingDown className="h-5 w-5 text-danger-600" />;
      case 'Hold':
        return <Minus className="h-5 w-5 text-warning-600" />;
      default:
        return <Minus className="h-5 w-5 text-gray-600" />;
    }
  };

  const getActionBadge = (action: string) => {
    switch (action) {
      case 'Buy':
        return 'badge-success';
      case 'Sell':
        return 'badge-danger';
      case 'Hold':
        return 'badge-warning';
      default:
        return 'badge-warning';
    }
  };

  const getSentimentBadge = (sentiment: string) => {
    switch (sentiment) {
      case 'Positive':
        return 'badge-success';
      case 'Negative':
        return 'badge-danger';
      case 'Neutral':
        return 'badge-warning';
      default:
        return 'badge-warning';
    }
  };

  return (
    <ErrorBoundary>
      <div className={`space-y-6 ${conditionalAnimation(animations.fadeIn)}`}>
        {/* Header */}
        <div className={`flex items-center justify-between ${conditionalAnimation(animations.fadeInDown)}`}>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Trading Dashboard</h1>
            <p className="text-gray-600 mt-1">
              Technical Analysis & News Sentiment for {summary.totalTickers} Tickers
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-500">
              Last updated: {new Date(dashboardData.lastUpdate).toLocaleString()}
            </div>
            <button
              onClick={() => window.location.reload()}
              className={`btn btn-secondary ${animations.buttonHover} ${animations.buttonPress}`}
              title="Refresh Dashboard"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>
        </div>

      {/* Summary Cards */}
      <div className={`grid grid-cols-1 md:grid-cols-4 gap-6 ${conditionalAnimation(animations.fadeInUp)}`}>
        <div className={`card ${animations.cardHover} ${conditionalAnimation(animations.fadeInUp)} delay-100`}>
          <div className="flex items-center">
            <div className={`p-2 bg-success-100 rounded-lg ${animations.hoverScale}`}>
              <TrendingUp className="h-6 w-6 text-success-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Buy Signals</p>
              <p className="text-2xl font-bold text-gray-900 transition-all duration-300">{summary.buySignals}</p>
            </div>
          </div>
        </div>

        <div className={`card ${animations.cardHover} ${conditionalAnimation(animations.fadeInUp)} delay-200`}>
          <div className="flex items-center">
            <div className={`p-2 bg-danger-100 rounded-lg ${animations.hoverScale}`}>
              <TrendingDown className="h-6 w-6 text-danger-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Sell Signals</p>
              <p className="text-2xl font-bold text-gray-900 transition-all duration-300">{summary.sellSignals}</p>
            </div>
          </div>
        </div>

        <div className={`card ${animations.cardHover} ${conditionalAnimation(animations.fadeInUp)} delay-300`}>
          <div className="flex items-center">
            <div className={`p-2 bg-warning-100 rounded-lg ${animations.hoverScale}`}>
              <Minus className="h-6 w-6 text-warning-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Hold Signals</p>
              <p className="text-2xl font-bold text-gray-900 transition-all duration-300">{summary.holdSignals}</p>
            </div>
          </div>
        </div>

        <div className={`card ${animations.cardHover} ${conditionalAnimation(animations.fadeInUp)} delay-500`}>
          <div className="flex items-center">
            <div className={`p-2 bg-primary-100 rounded-lg ${animations.hoverScale}`}>
              <Activity className="h-6 w-6 text-primary-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Avg Sentiment</p>
              <p className="text-2xl font-bold text-gray-900 transition-all duration-300">{summary.averageSentiment}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Ticker Grid */}
      <div className={`grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 ${conditionalAnimation(animations.fadeInUp)}`}>
        {tickers.map((ticker: TickerData, index: number) => (
          <Link
            key={ticker.ticker}
            to={`/ticker/${ticker.ticker}`}
            className={`card cursor-pointer ${animations.cardHover} ${animations.cardPress} ${conditionalAnimation(animations.fadeInUp)}`}
            style={{ animationDelay: `${index * 100}ms` }}
          >
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-xl font-bold text-gray-900">{ticker.ticker}</h3>
                <div className="flex items-center space-x-2 mt-1">
                  <DollarSign className="h-4 w-4 text-gray-400" />
                  <span className="text-lg font-semibold text-gray-900">
                    ${ticker.currentPrice.toFixed(2)}
                  </span>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {getActionIcon(ticker.recommendation.action)}
                <span className={`badge ${getActionBadge(ticker.recommendation.action)}`}>
                  {ticker.recommendation.action}
                </span>
              </div>
            </div>

            <div className="space-y-3">
              {/* Technical Indicators */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Technical Indicators</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">RSI:</span>
                    <span className="font-medium">{ticker.technicalIndicators.rsi.toFixed(1)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">ATR:</span>
                    <span className="font-medium">{ticker.technicalIndicators.atr.toFixed(2)}</span>
                  </div>
                </div>
              </div>

              {/* Sentiment */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Sentiment</h4>
                <div className="flex items-center justify-between">
                  <span className={`badge ${getSentimentBadge(ticker.recommendation.sentiment)}`}>
                    {ticker.recommendation.sentiment}
                  </span>
                  <span className="text-xs text-gray-500">
                    {ticker.news.length} articles
                  </span>
                </div>
              </div>

              {/* Confidence */}
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-1">Confidence</h4>
                <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
                  <div
                    className={`h-2 rounded-full transition-all duration-1000 ease-out ${
                      ticker.recommendation.confidence === 'High'
                        ? 'bg-success-500'
                        : ticker.recommendation.confidence === 'Medium'
                        ? 'bg-warning-500'
                        : 'bg-gray-400'
                    }`}
                    style={{
                      width: `${ticker.recommendation.confidence === 'High' ? 100 : ticker.recommendation.confidence === 'Medium' ? 66 : 33}%`,
                      animationDelay: `${index * 100 + 500}ms`
                    }}
                  ></div>
                </div>
                <span className="text-xs text-gray-500 mt-1 block">
                  {ticker.recommendation.confidence}
                </span>
              </div>
            </div>
          </Link>
        ))}
      </div>
      </div>
    </ErrorBoundary>
  );
};