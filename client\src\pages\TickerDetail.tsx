import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useQuery } from 'react-query';
import { ArrowLeft, TrendingUp, TrendingDown, Minus, DollarSign, AlertTriangle, Target, Shield, BarChart3, Activity } from 'lucide-react';
import { tickerApi } from '../services/api';
import { TickerData, OHLCV, AdvancedTechnicalIndicators } from '../types';
import { showToast } from '../components/Toaster';
import { CandlestickChart } from '../components/charts/CandlestickChart';
import { TechnicalIndicatorChart } from '../components/charts/TechnicalIndicatorChart';
import { PageLoader, SkeletonChart } from '../components/LoadingSpinner';
import { ErrorBoundary } from '../components/ErrorBoundary';
import { useState } from 'react';

export const TickerDetail = () => {
  const { ticker } = useParams<{ ticker: string }>();
  const [activeTab, setActiveTab] = useState<'overview' | 'charts' | 'indicators' | 'analysis'>('overview');

  const { data: tickerData, isLoading, error } = useQuery<TickerData>(
    ['ticker', ticker],
    () => tickerApi.getByTicker(ticker!),
    {
      enabled: !!ticker,
      onError: () => showToast('Failed to load ticker data', 'error'),
    }
  );

  const { data: historyData, isLoading: isLoadingHistory } = useQuery<OHLCV[]>(
    ['history', ticker],
    () => tickerApi.getHistory(ticker!),
    {
      enabled: !!ticker,
      onError: () => showToast('Failed to load chart data', 'error'),
    }
  );

  // Mock advanced technical indicators - in production, this would come from the API
  const mockAdvancedIndicators: AdvancedTechnicalIndicators = {
    ...tickerData?.technicalIndicators,
    fibonacci: {
      retracements: [
        { level: 0.236, price: (tickerData?.currentPrice || 100) * 0.95 },
        { level: 0.382, price: (tickerData?.currentPrice || 100) * 0.92 },
        { level: 0.5, price: (tickerData?.currentPrice || 100) * 0.90 },
        { level: 0.618, price: (tickerData?.currentPrice || 100) * 0.88 },
      ],
      extensions: [
        { level: 1.272, price: (tickerData?.currentPrice || 100) * 1.15 },
        { level: 1.618, price: (tickerData?.currentPrice || 100) * 1.20 },
      ],
    },
    supportResistance: {
      support: [(tickerData?.currentPrice || 100) * 0.95, (tickerData?.currentPrice || 100) * 0.90],
      resistance: [(tickerData?.currentPrice || 100) * 1.05, (tickerData?.currentPrice || 100) * 1.10],
      pivotPoint: (tickerData?.currentPrice || 100),
    },
    volumeAnalysis: {
      obv: 1500000,
      volumeProfile: [],
      volumeTrend: 'increasing' as const,
    },
    patternRecognition: {
      patterns: ['Ascending Triangle', 'Bullish Flag'],
      trendDirection: 'uptrend' as const,
      trendStrength: 75,
    },
    multiTimeframe: {
      '1h': { rsi: tickerData?.technicalIndicators.rsi || 50 },
      '4h': { rsi: tickerData?.technicalIndicators.rsi || 50 },
      '1d': { rsi: tickerData?.technicalIndicators.rsi || 50 },
    },
    stochastic: {
      k: 65,
      d: 62,
      signal: 'neutral' as const,
    },
    williamsR: {
      value: -35,
      signal: 'neutral' as const,
    },
  } as AdvancedTechnicalIndicators;

  if (isLoading || isLoadingHistory) {
    return <PageLoader text={`Loading ${ticker} data...`} />;
  }

  if (error || !tickerData) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 text-lg font-medium">Failed to load ticker data</div>
        <div className="text-gray-600 mt-2">Please try again later</div>
        <Link to="/" className="btn btn-primary mt-4 inline-block">
          Back to Dashboard
        </Link>
      </div>
    );
  }

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'Buy':
        return <TrendingUp className="h-6 w-6 text-success-600" />;
      case 'Sell':
        return <TrendingDown className="h-6 w-6 text-danger-600" />;
      case 'Hold':
        return <Minus className="h-6 w-6 text-warning-600" />;
      default:
        return <Minus className="h-6 w-6 text-gray-600" />;
    }
  };

  const getActionBadge = (action: string) => {
    switch (action) {
      case 'Buy':
        return 'badge-success';
      case 'Sell':
        return 'badge-danger';
      case 'Hold':
        return 'badge-warning';
      default:
        return 'badge-warning';
    }
  };

  const getSentimentBadge = (sentiment: string) => {
    switch (sentiment) {
      case 'Positive':
        return 'badge-success';
      case 'Negative':
        return 'badge-danger';
      case 'Neutral':
        return 'badge-warning';
      default:
        return 'badge-warning';
    }
  };

  const getSignalBadge = (signal: string) => {
    switch (signal) {
      case 'bullish':
        return 'badge-success';
      case 'bearish':
        return 'badge-danger';
      case 'neutral':
        return 'badge-warning';
      default:
        return 'badge-warning';
    }
  };

  return (
    <ErrorBoundary>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link to="/" className="btn btn-secondary">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Link>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{tickerData.ticker}</h1>
              <p className="text-gray-600">Professional Trading Analysis & Charts</p>
            </div>
          </div>
          <div className="text-sm text-gray-500">
            Last updated: {new Date(tickerData.lastUpdated).toLocaleString()}
          </div>
        </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('overview')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'overview'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Activity className="h-4 w-4 inline mr-2" />
            Overview
          </button>
          <button
            onClick={() => setActiveTab('charts')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'charts'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <BarChart3 className="h-4 w-4 inline mr-2" />
            Price Charts
          </button>
          <button
            onClick={() => setActiveTab('indicators')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'indicators'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <TrendingUp className="h-4 w-4 inline mr-2" />
            Technical Indicators
          </button>
          <button
            onClick={() => setActiveTab('analysis')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'analysis'
                ? 'border-primary-500 text-primary-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Target className="h-4 w-4 inline mr-2" />
            Advanced Analysis
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Current Price and Recommendation */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="card">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Current Price</h3>
                  <div className="flex items-center space-x-2 mt-2">
                    <DollarSign className="h-5 w-5 text-gray-400" />
                    <span className="text-3xl font-bold text-gray-900">
                      ${tickerData.currentPrice.toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="card">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Recommendation</h3>
                  <div className="flex items-center space-x-3 mt-2">
                    {getActionIcon(tickerData.recommendation.action)}
                    <span className={`badge text-sm ${getActionBadge(tickerData.recommendation.action)}`}>
                      {tickerData.recommendation.action}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="card">
              <div>
                <h3 className="text-lg font-medium text-gray-900">Confidence</h3>
                <div className="mt-2">
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className={`h-3 rounded-full ${
                        tickerData.recommendation.confidence === 'High'
                          ? 'bg-success-500'
                          : tickerData.recommendation.confidence === 'Medium'
                          ? 'bg-warning-500'
                          : 'bg-gray-400'
                      }`}
                      style={{
                        width: `${tickerData.recommendation.confidence === 'High' ? 100 : tickerData.recommendation.confidence === 'Medium' ? 66 : 33}%`
                      }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 mt-1 block">
                    {tickerData.recommendation.confidence} Confidence
                  </span>
                </div>
              </div>
            </div>
          </div>

      {/* Technical Analysis */}
      <div className="card">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Technical Analysis</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">RSI (14)</h4>
            <div className="text-2xl font-bold text-gray-900">
              {tickerData.technicalIndicators.rsi.toFixed(1)}
            </div>
            <span className={`badge ${getSignalBadge(tickerData.recommendation.technicalSignals.rsi)}`}>
              {tickerData.recommendation.technicalSignals.rsi}
            </span>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">MACD</h4>
            <div className="space-y-1">
              <div className="text-sm">
                <span className="text-gray-600">MACD:</span>
                <span className="font-medium ml-2">{tickerData.technicalIndicators.macd.macd.toFixed(3)}</span>
              </div>
              <div className="text-sm">
                <span className="text-gray-600">Signal:</span>
                <span className="font-medium ml-2">{tickerData.technicalIndicators.macd.signal.toFixed(3)}</span>
              </div>
            </div>
            <span className={`badge ${getSignalBadge(tickerData.recommendation.technicalSignals.macd)}`}>
              {tickerData.recommendation.technicalSignals.macd}
            </span>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Bollinger Bands</h4>
            <div className="space-y-1">
              <div className="text-sm">
                <span className="text-gray-600">Upper:</span>
                <span className="font-medium ml-2">${tickerData.technicalIndicators.bollingerBands.upper.toFixed(2)}</span>
              </div>
              <div className="text-sm">
                <span className="text-gray-600">Lower:</span>
                <span className="font-medium ml-2">${tickerData.technicalIndicators.bollingerBands.lower.toFixed(2)}</span>
              </div>
            </div>
            <span className={`badge ${getSignalBadge(tickerData.recommendation.technicalSignals.bollingerBands)}`}>
              {tickerData.recommendation.technicalSignals.bollingerBands}
            </span>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">SMA Crossover</h4>
            <div className="space-y-1">
              <div className="text-sm">
                <span className="text-gray-600">20-day:</span>
                <span className="font-medium ml-2">${tickerData.technicalIndicators.sma.short.toFixed(2)}</span>
              </div>
              <div className="text-sm">
                <span className="text-gray-600">50-day:</span>
                <span className="font-medium ml-2">${tickerData.technicalIndicators.sma.long.toFixed(2)}</span>
              </div>
            </div>
            <span className={`badge ${getSignalBadge(tickerData.recommendation.technicalSignals.sma)}`}>
              {tickerData.recommendation.technicalSignals.sma}
            </span>
          </div>
        </div>
      </div>

      {/* Risk Management */}
      <div className="card">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Risk Management</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="h-8 w-8 text-danger-500" />
            <div>
              <h4 className="text-sm font-medium text-gray-700">Stop Loss</h4>
              <div className="text-lg font-bold text-gray-900">
                ${tickerData.recommendation.riskManagement.stopLoss.toFixed(2)}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <Target className="h-8 w-8 text-success-500" />
            <div>
              <h4 className="text-sm font-medium text-gray-700">Take Profit</h4>
              <div className="text-lg font-bold text-gray-900">
                ${tickerData.recommendation.riskManagement.takeProfit.toFixed(2)}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <DollarSign className="h-8 w-8 text-primary-500" />
            <div>
              <h4 className="text-sm font-medium text-gray-700">Position Size</h4>
              <div className="text-lg font-bold text-gray-900">
                ${tickerData.recommendation.riskManagement.positionSize.toLocaleString()}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <Shield className="h-8 w-8 text-warning-500" />
            <div>
              <h4 className="text-sm font-medium text-gray-700">Max Risk</h4>
              <div className="text-lg font-bold text-gray-900">
                {(tickerData.recommendation.riskManagement.maxRisk * 100).toFixed(1)}%
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Sentiment Analysis */}
      <div className="card">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Sentiment Analysis</h2>
        <div className="mb-4">
          <div className="flex items-center space-x-3">
            <span className={`badge ${getSentimentBadge(tickerData.recommendation.sentiment)}`}>
              {tickerData.recommendation.sentiment} Sentiment
            </span>
            <span className="text-sm text-gray-600">
              Based on {tickerData.news.length} news articles
            </span>
          </div>
        </div>
        <p className="text-gray-700">{tickerData.recommendation.reasoning}</p>
      </div>

      {/* News Articles */}
      <div className="card">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Recent News</h2>
        <div className="space-y-4">
          {tickerData.news.slice(0, 5).map((article, index) => (
            <div key={index} className="border-b border-gray-200 pb-4 last:border-b-0">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 mb-1">
                    <a
                      href={article.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:text-primary-600 transition-colors"
                    >
                      {article.title}
                    </a>
                  </h4>
                  <p className="text-sm text-gray-600 mb-2">{article.content.substring(0, 150)}...</p>
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span>{article.source}</span>
                    <span>{new Date(article.publishedAt).toLocaleDateString()}</span>
                    {article.sentiment && (
                      <span className={`badge ${getSentimentBadge(article.sentiment.sentiment)}`}>
                        {article.sentiment.sentiment}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
        </div>
      )}

      {activeTab === 'charts' && (
        <div className="space-y-6">
          {historyData ? (
            <CandlestickChart
              data={historyData}
              indicators={mockAdvancedIndicators}
              ticker={tickerData.ticker}
              height={600}
              showVolume={true}
              showIndicators={true}
            />
          ) : (
            <SkeletonChart height={600} />
          )}

          {/* Fibonacci Levels */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Fibonacci Levels</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3">Retracements</h4>
                <div className="space-y-2">
                  {mockAdvancedIndicators.fibonacci.retracements.map((level, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">{(level.level * 100).toFixed(1)}%</span>
                      <span className="text-sm font-medium">${level.price.toFixed(2)}</span>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-3">Extensions</h4>
                <div className="space-y-2">
                  {mockAdvancedIndicators.fibonacci.extensions.map((level, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">{(level.level * 100).toFixed(1)}%</span>
                      <span className="text-sm font-medium">${level.price.toFixed(2)}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Support and Resistance */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Support & Resistance Levels</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="text-sm font-medium text-success-700 mb-3">Support Levels</h4>
                <div className="space-y-2">
                  {mockAdvancedIndicators.supportResistance.support.map((level, index) => (
                    <div key={index} className="text-sm font-medium text-success-600">
                      ${level.toFixed(2)}
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-primary-700 mb-3">Pivot Point</h4>
                <div className="text-lg font-bold text-primary-600">
                  ${mockAdvancedIndicators.supportResistance.pivotPoint.toFixed(2)}
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-danger-700 mb-3">Resistance Levels</h4>
                <div className="space-y-2">
                  {mockAdvancedIndicators.supportResistance.resistance.map((level, index) => (
                    <div key={index} className="text-sm font-medium text-danger-600">
                      ${level.toFixed(2)}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'indicators' && (
        <div className="space-y-6">
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Technical Indicators</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-sm font-medium text-gray-700">RSI (14)</div>
                <div className={`text-2xl font-bold ${
                  mockAdvancedIndicators.rsi > 70 ? 'text-danger-600' :
                  mockAdvancedIndicators.rsi < 30 ? 'text-success-600' : 'text-gray-900'
                }`}>
                  {mockAdvancedIndicators.rsi.toFixed(1)}
                </div>
                <div className={`text-xs px-2 py-1 rounded-full mt-1 ${
                  mockAdvancedIndicators.rsi > 70 ? 'bg-danger-100 text-danger-800' :
                  mockAdvancedIndicators.rsi < 30 ? 'bg-success-100 text-success-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {mockAdvancedIndicators.rsi > 70 ? 'Overbought' : mockAdvancedIndicators.rsi < 30 ? 'Oversold' : 'Neutral'}
                </div>
              </div>

              <div className="text-center">
                <div className="text-sm font-medium text-gray-700">MACD</div>
                <div className={`text-2xl font-bold ${
                  mockAdvancedIndicators.macd.histogram > 0 ? 'text-success-600' : 'text-danger-600'
                }`}>
                  {mockAdvancedIndicators.macd.macd.toFixed(3)}
                </div>
                <div className={`text-xs px-2 py-1 rounded-full mt-1 ${
                  mockAdvancedIndicators.macd.histogram > 0 ? 'bg-success-100 text-success-800' : 'bg-danger-100 text-danger-800'
                }`}>
                  {mockAdvancedIndicators.macd.histogram > 0 ? 'Bullish' : 'Bearish'}
                </div>
              </div>

              <div className="text-center">
                <div className="text-sm font-medium text-gray-700">Stochastic %K</div>
                <div className={`text-2xl font-bold ${
                  mockAdvancedIndicators.stochastic.k > 80 ? 'text-danger-600' :
                  mockAdvancedIndicators.stochastic.k < 20 ? 'text-success-600' : 'text-gray-900'
                }`}>
                  {mockAdvancedIndicators.stochastic.k.toFixed(1)}
                </div>
                <div className={`text-xs px-2 py-1 rounded-full mt-1 ${
                  mockAdvancedIndicators.stochastic.k > 80 ? 'bg-danger-100 text-danger-800' :
                  mockAdvancedIndicators.stochastic.k < 20 ? 'bg-success-100 text-success-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {mockAdvancedIndicators.stochastic.k > 80 ? 'Overbought' : mockAdvancedIndicators.stochastic.k < 20 ? 'Oversold' : 'Neutral'}
                </div>
              </div>

              <div className="text-center">
                <div className="text-sm font-medium text-gray-700">Williams %R</div>
                <div className={`text-2xl font-bold ${
                  mockAdvancedIndicators.williamsR.value < -80 ? 'text-success-600' :
                  mockAdvancedIndicators.williamsR.value > -20 ? 'text-danger-600' : 'text-gray-900'
                }`}>
                  {mockAdvancedIndicators.williamsR.value.toFixed(1)}
                </div>
                <div className={`text-xs px-2 py-1 rounded-full mt-1 ${
                  mockAdvancedIndicators.williamsR.value < -80 ? 'bg-success-100 text-success-800' :
                  mockAdvancedIndicators.williamsR.value > -20 ? 'bg-danger-100 text-danger-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {mockAdvancedIndicators.williamsR.value < -80 ? 'Oversold' : mockAdvancedIndicators.williamsR.value > -20 ? 'Overbought' : 'Neutral'}
                </div>
              </div>
            </div>
          </div>

          {/* Interactive Technical Indicator Charts */}
          {historyData && (
            <TechnicalIndicatorChart
              indicators={mockAdvancedIndicators}
              timeData={historyData.map(d => (d as any).date || d.timestamp)}
              height={900}
            />
          )}

          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Volume & Trend Analysis</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <div className="text-sm font-medium text-gray-700 mb-2">On-Balance Volume</div>
                <div className="text-xl font-bold text-gray-900">
                  {mockAdvancedIndicators.volumeAnalysis.obv.toLocaleString()}
                </div>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-700 mb-2">Volume Trend</div>
                <span className={`badge ${
                  mockAdvancedIndicators.volumeAnalysis.volumeTrend === 'increasing' ? 'badge-success' :
                  mockAdvancedIndicators.volumeAnalysis.volumeTrend === 'decreasing' ? 'badge-danger' : 'badge-warning'
                }`}>
                  {mockAdvancedIndicators.volumeAnalysis.volumeTrend}
                </span>
              </div>
              <div>
                <div className="text-sm font-medium text-gray-700 mb-2">ATR (Volatility)</div>
                <div className="text-xl font-bold text-gray-900">
                  {mockAdvancedIndicators.atr.toFixed(2)}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'analysis' && (
        <div className="space-y-6">
          {/* Pattern Recognition */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Pattern Recognition</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Detected Patterns</h4>
                <div className="space-y-1">
                  {mockAdvancedIndicators.patternRecognition.patterns.map((pattern, index) => (
                    <span key={index} className="inline-block bg-primary-100 text-primary-800 text-xs px-2 py-1 rounded mr-2 mb-1">
                      {pattern}
                    </span>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Trend Direction</h4>
                <span className={`badge ${
                  mockAdvancedIndicators.patternRecognition.trendDirection === 'uptrend' ? 'badge-success' :
                  mockAdvancedIndicators.patternRecognition.trendDirection === 'downtrend' ? 'badge-danger' : 'badge-warning'
                }`}>
                  {mockAdvancedIndicators.patternRecognition.trendDirection}
                </span>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Trend Strength</h4>
                <div className="flex items-center space-x-2">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="h-2 rounded-full bg-primary-500"
                      style={{ width: `${mockAdvancedIndicators.patternRecognition.trendStrength}%` }}
                    ></div>
                  </div>
                  <span className="text-sm font-medium">{mockAdvancedIndicators.patternRecognition.trendStrength}%</span>
                </div>
              </div>
            </div>
          </div>

          {/* Volume Analysis */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Volume Analysis</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">On-Balance Volume</h4>
                <div className="text-lg font-bold text-gray-900">
                  {mockAdvancedIndicators.volumeAnalysis.obv.toLocaleString()}
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Volume Trend</h4>
                <span className={`badge ${
                  mockAdvancedIndicators.volumeAnalysis.volumeTrend === 'increasing' ? 'badge-success' :
                  mockAdvancedIndicators.volumeAnalysis.volumeTrend === 'decreasing' ? 'badge-danger' : 'badge-warning'
                }`}>
                  {mockAdvancedIndicators.volumeAnalysis.volumeTrend}
                </span>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Volume Signal</h4>
                <div className="text-sm text-gray-600">
                  {mockAdvancedIndicators.volumeAnalysis.volumeTrend === 'increasing'
                    ? 'Strong institutional participation'
                    : 'Declining interest'}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </ErrorBoundary>
  );
};